NGINX_ENVSUBST_TEMPLATE_SUFFIX=.tpl

VICLASS_REMOTE=***********
VICLASS_LOCAL=host.docker.internal

# local nginx
VICLASS_LOCAL_NGINX_HOST=host.docker.internal
VICLASS_LOCAL_NGINX_PORT=280
VICLASS_LOCAL_NGINX_ENABLE=false

# Port that browser connects to to access the nginx inside docker
NGINX_CONNECT_PORT=443
# Port that the nginx inside docker is listening to
NGINX_LISTEN_PORT=443

# Use in local config to proxy to local service
# Connect port are port
PORTAL_BACKEND_LISTEN_PORT=9000
PORTAL_FILESTORE_HTTP_LISTEN_PORT=10000
# Not use, the portal is accessed through /api path of the nginx,
# so services doesn't connect to it directly
# PORTAL_BACKEND_CONNECT_PORT=19000
PORTAL_USER_LISTEN_PORT=1122
PORTAL_USER_CONNECT_PORT=11122
PORTAL_LSESSION_LISTEN_PORT=1133
PORTAL_LSESSION_CONNECT_PORT=11133
PORTAL_CONFIGURATION_LISTEN_PORT=1144
PORTAL_CONFIGURATION_CONNECT_PORT=11144
PORTAL_NOTIFICATION_LISTEN_PORT=1155
PORTAL_NOTIFICATION_CONNECT_PORT=11155
PORTAL_METADATA_LISTEN_PORT=1166
PORTAL_METADATA_CONNECT_PORT=11166
PORTAL_FILESTORE_LISTEN_PORT=1177
PORTAL_FILESTORE_CONNECT_PORT=11177
PORTAL_BETA_LISTEN_PORT=1188
PORTAL_BETA_CONNECT_PORT=11188
PORTAL_SHORTURL_LISTEN_PORT=1199
PORTAL_SHORTURL_CONNECT_PORT=11199
PORTAL_JOBRUNR_LISTEN_PORT=4007
PORTAL_JOBRUNR_CONNECT_PORT=14007
VINET_CCS_LISTEN_PORT=8000
VINET_CCS_CONNECT_PORT=18000
MONGO_LISTEN_PORT=27017
MONGO_CONNECT_PORT=27018
KAFKA_LISTEN_PORT=9092
KAFKA_CONNECT_PORT=19092
REDIS_LISTEN_PORT=6379
REDIS_CONNECT_PORT=16379

FREEDRAWING_BACKEND_LISTEN_PORT=8010
FREEDRAWING_BACKEND_CONNECT_PORT=18010

GEOMETRY_BACKEND_LISTEN_PORT=8011
GEOMETRY_BACKEND_CONNECT_PORT=18011

WORD_BACKEND_LISTEN_PORT=8012
WORD_BACKEND_CONNECT_PORT=18012

MATH_BACKEND_LISTEN_PORT=8013
MATH_BACKEND_CONNECT_PORT=18013

MAGH_BACKEND_LISTEN_PORT=8014
MAGH_BACKEND_CONNECT_PORT=18014

CONF_BACKEND_LISTEN_PORT=7000
CONF_BACKEND_CONNECT_PORT=17000

CONF_STATIC=static.remote
CONF_HOMEPAGE=homepage.local
CONF_LSESSION=lsession.local
CONF_CLASSROOM=classroom.local
CONF_SUPPORT=support.local
CONF_API=api.remote
CONF_FILESTORE=filestore.remote
CONF_CONF=conf.remote

# Editor Implementations
CONF_MODULE_FREEDRAWING=module.freedrawing.local
CONF_MODULE_GEO=module.geo.local
CONF_MODULE_WORD=module.word.local
CONF_MODULE_MATH=module.math.local
CONF_MODULE_MAGH=module.magh.local

# EditorUI implementations
CONF_MODULE_EDIORUI_FREEDRAWING=module.editorui.freedrawing.local
CONF_MODULE_EDIORUI_GEO=module.editorui.geo.local
CONF_MODULE_EDIORUI_WORD=module.editorui.word.local
CONF_MODULE_EDIORUI_MATH=module.editorui.math.local
CONF_MODULE_EDIORUI_MAGH=module.editorui.magh.local

# Coordinator implementation
CONF_MODULE_COORDINATOR=module.coordinator.local
# Web wrappers
CONF_MODULE_WEB_WRAPPERS=module.web.wrappers.local
# Themes
CONF_MODULE_THEMES=module.themes.local

CONF_CCS=ccs-frontend.remote
CONF_CCS_BACKEND=ccs-backend.remote

CONF_METADATA=metadata-frontend.remote
CONF_METADATA_BACKEND=metadata-backend.remote

# backend implementations of the editors
CONF_FREEDRAWING=freedrawing.remote
CONF_FREEDRAWING_BACKEND=freedrawing.backend.remote
CONF_GEO=geo.remote
CONF_GEOMETRY_BACKEND=geometry.backend.remote
CONF_WORD=word.remote
CONF_WORD_BACKEND=word.backend.remote
CONF_MATH=math.remote
CONF_MATH_BACKEND=math.backend.remote
CONF_MAGH=magh.remote
CONF_MAGH_BACKEND=magh.backend.remote
CONF_CONF_BACKEND=conf.backend.remote

# Other services
CONF_PORTAL_CONFIGURATION=portal.configuration.remote
CONF_PORTAL_USER=portal.user.remote
CONF_PORTAL_LSESSION=portal.lsession.remote
CONF_PORTAL_NOTIFICATION=portal.notification.remote
CONF_PORTAL_FILESTORE=portal.filestore.remote
CONF_PORTAL_JOBRUNR=portal.jobrunr.remote

CONF_MONGO=mongo.remote
CONF_KAFKA=kafka.remote
CONF_REDIS=redis.remote

CONF_BETA=beta.remote
CONF_PORTAL_BETA=portal.beta.remote

CONF_SHORTURL=shorturl.remote
CONF_PORTAL_SHORTURL=portal.shorturl.remote
