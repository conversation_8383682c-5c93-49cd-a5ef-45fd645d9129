syntax = "proto3";

package viclass.proto.geometry.msg;

enum GeoKind {
  Unknown = 0;
  Geo2D = 1;
  Geo3D = 2;
}

message BoundaryProto {
  PositionProto start = 1;
  PositionProto end = 2;
}

message PositionProto {
  double x = 1;
  double y = 2;
  double z = 3;
}

message CoordsProto {
  repeated double coords = 1;
}

message RenderElFlagProto {
    bool usable = 4;
    optional bool deleted = 5;
    bool valid = 6;
    bool unselectable = 7;
}

message RenderVertexProto {
  int32 rel_index = 1;
  string name = 2;
  optional ElRenderPropsProto render_prop = 3;
  RenderElFlagProto flag = 4;
  CoordsProto coords = 9;
  optional string movement_path = 10;
}

enum LineTypeProto {
  UNKNOWN = 0;
  LINE = 1;
  RAY = 2;
  SEGMENT = 3;
  VECTOR = 4;
}

message RenderLineProto {
  int32 rel_index = 1;
  string el_type = 2;
  LineTypeProto line_type = 3;
  string name = 4;

  optional ElRenderPropsProto render_prop = 5;
  RenderElFlagProto flag = 6;

  int32 start_point_idx = 7;
  optional int32 end_point_idx = 8;

  repeated double vector = 9;

  // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---
  repeated int32 vertex_rel_idxes = 10;

  // --- SERVER CALCULATED FIELDS -----
  optional double length = 11;
}

message PreviewLineProto {
  RenderLineProto line = 1;
  /**
   * We include the point details inside the PreviewLineProto 
   * so that we can reconstruct the preview vertices on the peers 
   * should they not receive the preview vertices before when the points 
   * are being selected
   */
  repeated RenderVertexProto p_verts = 2;
  /**
   * We include the coords of the preview line in cases we 
   * don't care about the preview vertices and just want the line
   * to be rendered
   */
  CoordsProto s_v_coords = 3;
  CoordsProto e_v_coords = 4;
}

message RenderPolygonProto {
  int32 rel_index = 1;
  string el_type = 2;
  string name = 3;

  optional ElRenderPropsProto render_prop = 4;
  RenderElFlagProto flag = 5;

  repeated int32 faces = 6;

  // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---

  repeated int32 vertex_rel_idxes = 7;
  repeated int32 line_rel_idxes = 8;

  // ------- SERVER CALCULATED FIELDS -------
  double area = 9;
  double perimeter = 10;
}

message PreviewPolygonProto {

  RenderPolygonProto polygon = 1;
  repeated RenderVertexProto p_verts = 2;
  repeated PreviewLineProto p_edges = 3;

  map<int32, CoordsProto> p_coords = 4;
}

message RenderCircleProto {
  int32 rel_index = 1;
  string el_type = 2;
  string name = 3;

  optional ElRenderPropsProto render_prop = 4;
  RenderElFlagProto flag = 5;

  double radius = 6;
  int32 center_point_idx = 7; // index of the vertex that is the center
  
  // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---
  repeated int32 vertex_rel_idxes = 8;

  // ------- SERVER CALCULATED FIELDS -------
  optional double length = 10;
}

message PreviewCircleProto {
  RenderCircleProto circle = 1;

  RenderVertexProto c_vert = 2;
  
  CoordsProto c_coords = 3; // the coord that is the center
}

message RenderCircleShapeProto {
  int32 rel_index = 1;
  string el_type = 2;
  string name = 3;

  optional ElRenderPropsProto render_prop = 4;
  RenderElFlagProto flag = 5;

  double radius = 6;
  int32 center_point_idx = 7; // index of the vertex that is the center
  
  // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---
  repeated int32 vertex_rel_idxes = 8;
  int32 arc_idx = 9;

  // ------- SERVER CALCULATED FIELDS -------
  optional double area = 10;
  optional double perimeter = 11;
}

message PreviewCircleShapeProto {
  RenderCircleShapeProto circle = 1;

  RenderVertexProto c_vert = 2;
  optional RenderCircleProto arc = 3;
  
  CoordsProto c_coords = 4; // the coord that is the center
}


message RenderEllipseProto {
  int32 rel_index = 1;
  string el_type = 2;
  string name = 3;

  optional ElRenderPropsProto render_prop = 4;
  RenderElFlagProto flag = 5;

  int32 f1_idx = 6;
  int32 f2_idx = 7;
  double a = 8;
  double b = 9;
  double rotate = 10;

  // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---
  repeated int32 vertex_rel_idxes = 11;

  // ------- SERVER CALCULATED FIELDS -------
  optional double length = 12;
}
message PreviewEllipseProto {
  RenderEllipseProto ellipse = 1;

  optional RenderVertexProto f1_vert = 2;
  optional RenderVertexProto f2_vert = 3;

  // The preview coords of the focus points
  repeated CoordsProto p_coords = 4;
}

message RenderEllipseShapeProto {
  int32 rel_index = 1;
  string el_type = 2;
  string name = 3;

  optional ElRenderPropsProto render_prop = 4;
  RenderElFlagProto flag = 5;

  int32 f1_idx = 6;
  int32 f2_idx = 7;
  double a = 8;
  double b = 9;
  double rotate = 10;

  // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---
  repeated int32 vertex_rel_idxes = 11;
  int32 arc_idx = 12;

  // ------- SERVER CALCULATED FIELDS -------
  optional double perimeter = 13;
  optional double area = 14;
}
message PreviewEllipseShapeProto {
  RenderEllipseShapeProto ellipse = 1;

  optional RenderVertexProto f1_vert = 2;
  optional RenderVertexProto f2_vert = 3;
  optional RenderEllipseProto arc = 4;

  // The preview coords of the focus points
  optional CoordsProto f1_coords = 5;
  optional CoordsProto f2_coords = 6;
}

message RenderSectorProto {
  int32 rel_index = 1;
  string el_type = 2;
  string name = 3;

  optional ElRenderPropsProto render_prop = 4;
  RenderElFlagProto flag = 5;

  double radius = 6;

  int32 center_point_idx = 7;
  optional int32 start_point_idx = 8;
  optional int32 end_point_idx = 9;

  // --- RENDER ELEMENT AS PARTS OF RENDERING THIS ELEMENT ---
  repeated int32 vertex_rel_idxes = 10;
  
  // --- SERVER CALCULATED FIELDS ----
  optional double length = 11;
}

message PreviewSectorProto {
    RenderSectorShapeProto sector = 1;

    repeated RenderVertexProto p_verts = 2;
    optional CoordsProto c_coords = 3;
    optional CoordsProto s_coords = 4;
    optional CoordsProto e_coords = 5;
}

message RenderSectorShapeProto {
    int32 rel_index = 1;
    string el_type = 2;
    string name = 3;

    optional ElRenderPropsProto render_prop = 4;
    RenderElFlagProto flag = 5;

    double radius = 6;

    int32 center_point_idx = 7;
    optional int32 start_point_idx = 8;
    optional int32 end_point_idx = 9;

    // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---
    repeated int32 vertex_rel_idxes = 10;
    repeated int32 line_rel_idxes = 11;
    int32 arc_idx = 12;

    // --- SERVER CALCULATED FIELDS ----
    optional double area = 13;
    optional double length = 14;
}

message PreviewSectorShapeProto {
  RenderSectorShapeProto sector = 1;
  repeated RenderVertexProto p_verts = 2;
  optional CoordsProto c_coords = 3;
  optional CoordsProto s_coords = 4;
  optional CoordsProto e_coords = 5;
  optional RenderSectorProto arc = 6;
}

message RenderAngleProto {
  int32 rel_index = 1;
  string el_type = 2;
  string name = 3;

  optional ElRenderPropsProto render_prop = 4;
  RenderElFlagProto flag = 5;

  int32 angle_point_idx = 6;
  CoordsProto vector_start = 7;
  CoordsProto vector_end = 8;

  // --- RENDER ELEMENTS GENERATED AS PARTS OF RENDERING THIS ELEMENT ---
  repeated int32 vertex_rel_idxes = 9;
  repeated int32 line_rel_idxes = 10;

  // --- SERVER CALCULATED FIELDS ----
  optional double degree = 11;
}

message PreviewAngleProto {
  RenderAngleProto angle = 1;

  optional RenderVertexProto c_vert = 2;
  // if we just want to show the preview of angle
  // without rendering the preview elements, c_verts is empty
  // and set this c vert coord to be the coordinates
  // of the root vertex
  optional CoordsProto c_vert_coords = 3;
  // when creating preview angle, we need to have lines as well
  repeated RenderLineProto p_lines = 4;
}

message ElRenderPropsProto {
  optional string color = 1;
  optional string stroke_style = 2;
  optional uint32 line_weight = 3;
  optional bool hidden = 4;
  optional uint32 opacity = 5;
  optional string label = 6;
  optional bool swap_label_position = 7;
  optional bool show_background = 8;
  optional string name = 9;
  optional bool show_label = 10;
  optional string label_type = 11;
  optional float space_from_arc_to_corner = 12;
  optional string show_angle_types = 13;
  optional float size = 14;
  optional bool enable_equal_segment_sign = 15;
  optional string equal_segment_sign = 16;
  optional bool show_arc_label = 17;
  optional string arc_label_type = 18;
  optional string arc_label_content = 19;
  optional uint32 angle_arc = 20;
  optional bool is_show_angle_size = 21;
  optional string line_color = 22;
  optional string point_color = 23;
  optional string point_label_type = 24;
  optional string point_label_free_content = 25;
  optional bool show_point_label = 26;
}

message DocDefaultElRenderPropsProto {
  optional string color = 1;
  optional string stroke_style = 2;
  optional uint32 line_weight = 3;
  optional uint32 opacity = 4;
  optional bool show_background = 5;
  optional float space_from_arc_to_corner = 6;
  optional string line_color = 7;
  optional string point_color = 8;
}

message DocRenderPropProto {
  optional int32 screen_unit = 1;
  optional double canvas_width = 2;
  optional double canvas_height = 3;
  optional double scale = 4;
  repeated double translation = 5;
  repeated double rotation = 6;
  optional bool valid = 7;
  optional bool shadow = 8;
  optional string shadow_style = 9;
  optional bool axis = 10;
  optional bool grid = 11;
  optional bool detail_grid = 12;
  optional bool border = 13;
  optional string border_style = 14;
  optional string border_color = 15;
  optional bool snap_mode = 16;
  optional bool snap_to_existing_points = 17;
  optional bool snap_to_grid = 18;
  optional bool naming_mode = 19;
}
