# Tài liệu Selector Module

## Tổng quan

Selector Mo<PERSON><PERSON> là một hệ thống quản lý việc lựa chọn các phần tử hình học trong editor. Mo<PERSON>le này cung cấp một cách thức nhất quán và linh hoạt để xử lý việc chọn lựa các đối tượng hình học khác nhau như điểm (vertex), đ<PERSON>ờng thẳng (stroke), và các hình dạng phức tạp khác.

## Kiến trúc tổng thể

### C<PERSON>u trúc thư mục

```
src/lib/selectors/
├── index.ts                 # Export tất cả các selector
├── selectors.ts            # Base class và interfaces chính
├── selection.dsl.ts        # Domain Specific Language cho selection
├── stroke.selector.ts      # Selector cho các đường nét
├── vertex.selector.ts      # Selector cho các điểm
└── selector.types.ts       # Type definitions
```

## C<PERSON>c thành phần cơ bản

### 1. ElementSelector (<PERSON>ớ<PERSON> cơ sở)

`ElementSelector` là lớp cơ sở trừu tượng cho tất cả các selector trong hệ thống.

#### Thuộc tính chính:

- `multiple: boolean` - Xác định selector có thể chọn nhiều phần tử hay không
- `selected?: T` - Phần tử đã được chọn
- `curGeneratedId: number` - ID được tạo cho preview elements
- `options?: SelectorOptions<T>` - Các tùy chọn cấu hình

#### Phương thức chính:

- `tryHit(event, doc)` - Kiểm tra xem có phần tử nào được click không
- `tryPreview(event, doc)` - Tạo preview elements
- `trySelect(event, doc)` - Wrapper cho tryHit và tryPreview
- `reset(keepPreview)` - Reset trạng thái selector
- `finalizeAccept(doc)` - Hoàn tất việc chấp nhận selection

#### SelectorOptions:

```typescript
type SelectorOptions<T> = {
    stickyOnMatch?: boolean; // Tự động sticky khi match
    autoAcceptOnPointerUp?: boolean; // Tự động accept khi pointer up
    highlightOnMatch?: boolean; // Highlight khi match
    preview?: boolean; // Bao gồm preview elements
    renderEl?: boolean; // Chọn render elements
    genPreview?: boolean; // Tạo preview khi không match
    syncPreview?: boolean; // Đồng bộ preview
    refinedFilter?: (el) => boolean; // Filter function tùy chỉnh
    onComplete?: (selector, doc) => void; // Callback khi hoàn thành
};
```

### 2. VertexSelector

`VertexSelector` chuyên dụng để chọn các điểm (RenderVertex).

#### Tính năng đặc biệt:

- **Snap functionality**: Tự động snap đến các điểm gần nhất
- **Preview generation**: Tạo điểm preview khi không có điểm nào được chọn
- **Sticky selection**: Giữ điểm được chọn trong chế độ touch
- **Transform function**: Cho phép biến đổi điểm preview

#### Cách sử dụng:

```typescript
const vertexSelector = vertex({
    renderEl: true, // Chọn các điểm đã render
    preview: false, // Không bao gồm preview points
    genPreview: true, // Tạo preview point mới
    onComplete: (selector, doc) => {
        const selectedVertex = selector.selected;
        // Xử lý khi chọn xong
    },
});
```

#### VertexSelectorOptions:

```typescript
type VertexSelectorOptions = SelectorOptions<RenderVertex> & {
    stopSnap?: boolean; // Tắt snap
    tfunc?: (previewEl, doc) => RenderVertex; // Transform function
    cfunc?: (el) => boolean; // Validation function
};
```

### 3. StrokeSelector

`StrokeSelector` dùng để chọn các đường nét như line, circle, ellipse.

#### Các loại stroke được hỗ trợ:

- `RenderLine` - Đường thẳng
- `RenderLineSegment` - Đoạn thẳng
- `RenderVector` - Vector
- `RenderRay` - Tia
- `RenderSector` - Hình quạt
- `RenderEllipse` - Ellipse
- `RenderCircle` - Hình tròn

#### Cách sử dụng:

```typescript
const strokeSelector = stroke({
    selectableStrokeTypes: ['RenderLine', 'RenderCircle'],
    cfunc: el => el.visible, // Chỉ chọn các stroke visible
    onComplete: (selector, doc) => {
        const selectedStroke = selector.selected;
        // Xử lý stroke được chọn
    },
});
```

#### VertexOnStrokeSelector:

Selector đặc biệt để chọn một điểm trên stroke:

```typescript
const vertexOnStrokeSelector = vertexOnStroke({
    selectableStrokeTypes: ['RenderLine'],
    tfunc: (stroke, vertex, doc) => {
        // Transform vertex position on stroke
        return transformedVertex;
    },
    showPosHint: true, // Hiển thị hint về vị trí
});
```

## Selection DSL (Ngôn ngữ đặc thù miền)

Module cung cấp một DSL mạnh mẽ để tạo các logic selection phức tạp.

### 1. OrSelector

Chọn một trong nhiều loại phần tử:

```typescript
const orSelector = or([vertex({ preview: true }), stroke({ selectableStrokeTypes: ['RenderLine'] })], {
    flatten: true,
    onComplete: (selector, doc) => {
        // Xử lý khi chọn được vertex hoặc stroke
    },
});
```

### 2. ThenSelector

Chọn tuần tự các phần tử:

```typescript
const thenSelector = then(
    [
        vertex(), // Chọn vertex đầu tiên
        vertex(), // Sau đó chọn vertex thứ hai
    ],
    {
        onComplete: (selector, doc) => {
            const [vertex1, vertex2] = selector.selected;
            // Xử lý khi đã chọn đủ 2 vertex
        },
    }
);
```

### 3. RepeatSelector

Lặp lại việc chọn cùng một loại phần tử:

```typescript
const repeatSelector = repeat(vertex(), {
    count: 3, // Chọn 3 vertex
    onPartialSelection: (newVertex, currentVertices, selector, doc) => {
        // Callback mỗi khi chọn được 1 vertex
        return true; // Tiếp tục chọn
    },
    onComplete: (selector, doc) => {
        const vertices = selector.selected; // Array of 3 vertices
        // Xử lý khi đã chọn đủ 3 vertex
    },
});
```

## Tích hợp với Tools

Các selector được sử dụng rộng rãi trong các geometry tools:

### Ví dụ: CreatePointTool

```typescript
export class CreatePointTool extends GeometryTool<CommonToolState> {
    private selectionLogic: VertexSelector;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.selectionLogic = vertex({
            renderEl: false, // Không chọn điểm có sẵn
            onComplete: this.onPointSelected.bind(this),
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);

        if (event.eventType == 'pointermove') {
            this.selectionLogic.trySelect(event, ctrl);
        } else {
            this.selectionLogic.trySelect(event, ctrl);
        }

        return event;
    }

    async onPointSelected(selector: VertexSelector) {
        const point = selector.selected;
        // Tạo điểm mới tại vị trí được chọn
    }
}
```

### Ví dụ: CreateLineTool

```typescript
export class CreateLineTool extends GeometryTool<CommonToolState> {
    declare selLogic: RepeatSelector<SelectableType>;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.selLogic = repeat(
            or([vertex({ previewQueue: this.pQ }), vertexOnStroke({ previewQueue: this.pQ })], { flatten: true }),
            {
                count: 2,
                onComplete: (selector, doc) => this.performConstruction('Đường thẳng', doc, selector.selected),
            }
        );
    }
}
```

## Hệ thống Preview

Selector module tích hợp chặt chẽ với hệ thống preview:

### PreviewQueue

```typescript
const pQ = new PreviewQueue();

const selector = vertex({
    previewQueue: pQ,
    syncPreview: false, // Không sync ngay lập tức
});

// Flush tất cả preview elements
pQ.flush(docCtrl);
```

### Preview IDs

```typescript
export const PreviewIds = {
    stroke: -1000000,
    vertex: -10000000,
};
```

## Xử lý lỗi

Module sử dụng decorator pattern để xử lý lỗi:

```typescript
@ErrorHandlerDecorator([geoDefaultHandlerFn])
override tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): RenderVertex | undefined {
    // Implementation với error handling tự động
}
```

## Thực hành tốt nhất

### 1. Sử dụng DSL cho logic phức tạp

```typescript
// Tốt: Sử dụng DSL
const selector = repeat(or([vertex(), vertexOnStroke()]), { count: 3 });

// Không tốt: Tự implement logic phức tạp
```

### 2. Quản lý Preview đúng cách

```typescript
// Tốt: Sử dụng PreviewQueue
const pQ = new PreviewQueue();
const selector = vertex({ previewQueue: pQ });
pQ.flush(docCtrl);

// Không tốt: Sync preview ngay lập tức trong loop
```

### 3. Reset selector khi cần thiết

```typescript
override resetState(): void {
    this.selectionLogic.reset();
    super.resetState();
}
```

### 4. Sử dụng refinedFilter cho logic phức tạp

```typescript
const selector = vertex({
    refinedFilter: vertex => {
        return vertex.visible && vertex.coords[0] > 0;
    },
});
```

## Kết luận

Selector Module cung cấp một framework mạnh mẽ và linh hoạt để xử lý việc lựa chọn các phần tử hình học. Với DSL phong phú và hệ thống preview tích hợp, module này cho phép tạo ra các công cụ hình học phức tạp một cách dễ dàng và nhất quán.

Các tính năng chính:

- **Modular design**: Mỗi loại selector có trách nhiệm riêng biệt
- **DSL mạnh mẽ**: Tạo logic selection phức tạp một cách declarative
- **Preview system**: Hỗ trợ preview real-time
- **Error handling**: Xử lý lỗi tự động
- **Extensible**: Dễ dàng mở rộng cho các loại phần tử mới
