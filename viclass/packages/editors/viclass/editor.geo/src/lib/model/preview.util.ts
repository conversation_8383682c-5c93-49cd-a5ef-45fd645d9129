import { point, Point, Vector } from '@flatten-js/core';
import {
    GeoRenderElement,
    NOT_SET_VALUE,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
    RenderRay,
} from '.';
import { syncPreviewCommands } from '../cmd';
import { GeoDocCtrl } from '../objects';
import { GeoRenderer } from '../renderer';
import { GeoErr } from '../error-handler';

function validatePreviewId(id: number) {
    if (id > 0) throw new Error('Preview Id should be smaller than zero');
}

function idxOk(id?: number) {
    return id !== undefined && id !== NOT_SET_VALUE;
}

// Utility functions for create preview
export function pVertex(id: number, pos: number[]): RenderVertex {
    validatePreviewId(id);

    const el: RenderVertex = Object.assign(new RenderVertex(), {
        relIndex: id,
        coords: pos,
        name: undefined,
        usable: true,
        valid: true,
    });

    return el;
}

function addRefPEl(el: GeoRenderElement, refPEl: GeoRenderElement[]): boolean {
    if (el.relIndex < 0) {
        refPEl.push(el);
        return true;
    }

    return false;
}

/**
 * Create a line with preview information from start / end points or their coordinates
 * If a vector is supplied, then end point should be undefined
 * @param ctrl
 * @param id
 * @param clsK
 * @param startPoint
 * @param endPoint
 * @param vector
 * @returns RenerLine
 */
export function pLine(
    ctrl: GeoDocCtrl,
    id: number,
    clsK: new (...args) => RenderLine,
    startPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[] | undefined,
    vector: number[] | undefined = undefined
): RenderLine {
    const linePartial: Partial<RenderLine> = {
        relIndex: id,
        name: undefined,
        startPointIdx: startPoint instanceof RenderVertex ? startPoint.relIndex : NOT_SET_VALUE,
        endPointIdx: endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
            sVCoords: undefined,
            eVCoords: undefined,
        },
    };
    const el: RenderLine = Object.assign(new clsK(), linePartial);

    // if the point is added to refPEL, then we don't need to set the coord, else possibly
    let needSCoord = startPoint instanceof RenderVertex ? !addRefPEl(startPoint, el.pInfo.refPEl) : true;
    let needECoord = endPoint instanceof RenderVertex ? !addRefPEl(endPoint, el.pInfo.refPEl) : true;

    // if also exist in list of existing elements, don't need to include coords separately
    if (needSCoord && idxOk(el.startPointIdx) && ctrl.rendererCtrl.elementAt(el.startPointIdx)) needSCoord = false;
    if (needECoord && idxOk(el.endPointIdx) && ctrl.rendererCtrl.elementAt(el.endPointIdx)) needECoord = false;

    // still need the coord?
    if (needSCoord) el.pInfo.sVCoords = startPoint instanceof RenderVertex ? startPoint.coords : startPoint;
    if (needECoord) el.pInfo.eVCoords = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;

    // set the vector from the points
    const sCoord = el.coord('start', ctrl.rendererCtrl);
    const eCoord = el.coord('end', ctrl.rendererCtrl);

    el.vector = vector ? vector : [eCoord[0] - sCoord[0], eCoord[1] - sCoord[1], 0];

    return el;
}

/**
 * Create a ray with preview information from start point and optional end point or vector
 * A ray has a start point and extends infinitely in a direction defined by a vector or end point
 * @param ctrl
 * @param id
 * @param startPoint
 * @param endPoint - Optional end point, if not provided, vector should be used
 * @param vector - Direction vector, if endPoint is not provided
 * @returns RenderRay
 */
export function pRay(
    ctrl: GeoDocCtrl,
    id: number,
    startPoint: RenderVertex | number[],
    endPoint?: RenderVertex | number[] | undefined,
    vector?: number[] | undefined
): RenderRay {
    validatePreviewId(id);

    const rayPartial: Partial<RenderRay> = {
        relIndex: id,
        name: undefined,
        startPointIdx: startPoint instanceof RenderVertex ? startPoint.relIndex : NOT_SET_VALUE,
        endPointIdx: endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
            sVCoords: undefined,
            eVCoords: undefined,
        },
    };
    const el: RenderRay = Object.assign(new RenderRay(), rayPartial);

    // if the point is added to refPEL, then we don't need to set the coord, else possibly
    let needSCoord = startPoint instanceof RenderVertex ? !addRefPEl(startPoint, el.pInfo.refPEl) : true;
    let needECoord = endPoint instanceof RenderVertex ? !addRefPEl(endPoint, el.pInfo.refPEl) : true;

    // if also exist in list of existing elements, don't need to include coords separately
    if (needSCoord && idxOk(el.startPointIdx) && ctrl.rendererCtrl.elementAt(el.startPointIdx)) needSCoord = false;
    if (needECoord && idxOk(el.endPointIdx) && ctrl.rendererCtrl.elementAt(el.endPointIdx)) needECoord = false;

    // still need the coord?
    if (needSCoord) el.pInfo.sVCoords = startPoint instanceof RenderVertex ? startPoint.coords : startPoint;
    if (needECoord && endPoint) el.pInfo.eVCoords = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;

    // set the vector from the points or use provided vector
    const sCoord = el.coord('start', ctrl.rendererCtrl);

    if (vector) {
        // Use provided vector
        el.vector = [...vector];
        if (el.vector.length === 2) el.vector.push(0); // ensure 3D vector
    } else if (endPoint) {
        // Calculate vector from start to end point
        const eCoord = el.coord('end', ctrl.rendererCtrl);
        el.vector = [eCoord[0] - sCoord[0], eCoord[1] - sCoord[1], 0];
    } else {
        throw new Error('Ray requires either an end point or a direction vector');
    }

    return el;
}

/**
 * Internal helper that builds any "circle-like" element (RenderCircle | RenderCircleShape)
 * – The only thing that can vary is the concrete constructor and extra fields (e.g. `arcRelIdx`).
 */
function buildCircleLike<T extends RenderCircle | RenderCircleShape>(
    ctor: new () => T,
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    radius: number | undefined = undefined,
    extra: Partial<T> = {} // allow caller to inject specialised props
): T {
    const relIdxs = [
        centerPoint instanceof RenderVertex ? centerPoint.relIndex : NOT_SET_VALUE,
        endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
    ];

    const base: Partial<T> = {
        relIndex: id,
        name: '',
        centerPointIdx: relIdxs[0],
        vertexRelIdxes: relIdxs,
        radius: radius,
        usable: true,
        valid: true,
        pInfo: { refPEl: [], cCoords: undefined },
        ...extra,
    };

    const el = Object.assign(new ctor(), base);

    // reference-point bookkeeping
    let needCoords = centerPoint instanceof RenderVertex ? !addRefPEl(centerPoint, el.pInfo.refPEl) : true;

    if (needCoords && idxOk(el.centerPointIdx) && ctrl.rendererCtrl.elementAt(el.centerPointIdx)) {
        needCoords = false;
    }

    if (needCoords) {
        el.pInfo.cCoords = centerPoint instanceof RenderVertex ? centerPoint.coords : centerPoint;
    }

    // radius from centre → end
    const c = el.coord('center', ctrl.rendererCtrl);
    if (radius != null) {
        el.radius = radius;
    } else {
        const e = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;
        el.radius = point(c[0], c[1]).distanceTo(point(e[0], e[1]))[0];
    }

    return el;
}

/** Original `pCircle` now just delegates to the helper. */
export function pCircle(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    endPoint?: RenderVertex | number[] | undefined,
    radius: number | undefined = undefined
): RenderCircle {
    return buildCircleLike(RenderCircle, ctrl, id, centerPoint, endPoint, radius);
}

/** Original `pCircleShape` with the only extra bit: `arcRelIdx`. */
export function pCircleShape(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    arc: RenderCircle | number,
    endPoint: RenderVertex | number[] = undefined,
    radius: number | undefined = undefined
): RenderCircleShape {
    const el = buildCircleLike(RenderCircleShape, ctrl, id, centerPoint, endPoint, radius, {
        arcRelIdx: arc instanceof RenderCircle ? arc.relIndex : arc,
    } as Partial<RenderCircleShape>);

    if (arc instanceof RenderCircle) addRefPEl(arc, el.pInfo.refPEl);

    return el;
}

/**
 *
 * @param ctrl
 * @param id
 * @param verts
 * @param withBoundary
 * @param lineType
 * @returns
 */
export function pPolygon(
    ctrl: GeoDocCtrl,
    id: number,
    verts: (RenderVertex | number[])[],
    withBoundary: boolean,
    lineType?: new (...args: any[]) => RenderLine,
    lineIdGenerator?: (edgeNo: number) => number
) {
    const polygon = new RenderPolygon();
    const polygonPartial: Partial<RenderPolygon> = {
        relIndex: id,
        name: '',
        usable: true,
        valid: true,
        faces: verts.map(v => (v instanceof RenderVertex ? v.relIndex : NOT_SET_VALUE)),
        pInfo: {
            refPEl: [],
            verts: new Map(),
        },
    };

    verts.forEach((v, i) => {
        // for each vert, we try to use it
        if (v instanceof RenderVertex) {
            const addedRef = addRefPEl(v, polygonPartial.pInfo.refPEl);

            if (!addedRef) polygonPartial.pInfo.verts.set(i, [...v.coords]);
        } else {
            polygonPartial.pInfo.verts.set(i, [...v]);
        }

        if (withBoundary && i < verts.length) {
            const j = (i + 1) % verts.length;
            const edge = pLine(
                ctrl,
                lineIdGenerator ? lineIdGenerator(i) : id - j - 5,
                lineType ? lineType : RenderLineSegment,
                verts[i],
                verts[j],
                undefined
            );
            addRefPEl(edge, polygonPartial.pInfo.refPEl);
        }
    });

    Object.assign(polygon, polygonPartial);

    return polygon;
}

function createSectorElement<T extends RenderSector | RenderSectorShape>(
    ctrl: GeoDocCtrl,
    id: number,
    startPoint: RenderVertex | number[],
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    ElementCtor: new () => T,
    radius: number | undefined = undefined,
    extra?: GeoRenderElement
): T {
    const partial: Partial<T> = {
        relIndex: id,
        name: '',
        usable: true,
        valid: true,
        startPointIdx: startPoint instanceof RenderVertex ? startPoint.relIndex : NOT_SET_VALUE,
        centerPointIdx: centerPoint instanceof RenderVertex ? centerPoint.relIndex : NOT_SET_VALUE,
        endPointIdx: endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
        pInfo: {
            refPEl: [],
            sCoords: undefined,
            cCoords: undefined,
            eCoords: undefined,
        },
    } as T;

    if (extra) partial.pInfo!.refPEl.push(extra);

    const el: T = Object.assign(new ElementCtor(), partial);

    const c = centerPoint instanceof RenderVertex ? centerPoint.coords : centerPoint;
    const s = startPoint instanceof RenderVertex ? startPoint.coords : startPoint;
    const e = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;

    const pC = point(c[0], c[1]);
    const pS = point(s[0], s[1]);

    let needSCoord = startPoint instanceof RenderVertex ? !addRefPEl(startPoint, el.pInfo.refPEl) : true;
    let needCCoord = centerPoint instanceof RenderVertex ? !addRefPEl(centerPoint, el.pInfo.refPEl) : true;
    let needECoord = endPoint instanceof RenderVertex ? !addRefPEl(endPoint, el.pInfo.refPEl) : true;

    if (needSCoord && idxOk(el.startPointIdx) && ctrl.rendererCtrl.elementAt(el.startPointIdx)) needSCoord = false;
    if (needCCoord && idxOk(el.centerPointIdx) && ctrl.rendererCtrl.elementAt(el.centerPointIdx)) needCCoord = false;
    if (needECoord && idxOk(el.endPointIdx) && ctrl.rendererCtrl.elementAt(el.endPointIdx)) needECoord = false;

    if (needSCoord) el.pInfo.sCoords = s;
    if (needCCoord) el.pInfo.cCoords = c;
    if (needECoord) el.pInfo.eCoords = e;

    // Radius calculation consistent with buildCircleLike pattern
    if (radius != null) {
        el.radius = radius;
    } else {
        // For sector, radius is typically from center to start point
        el.radius = pC.distanceTo(pS)[0];
    }

    return el;
}

export function pSectorShape(
    ctrl: GeoDocCtrl,
    id: number,
    startPoint: RenderVertex | number[],
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    extra?: GeoRenderElement,
    radius: number | undefined = undefined
) {
    return createSectorElement(ctrl, id, startPoint, centerPoint, endPoint, RenderSectorShape, radius, extra);
}

export function pSector(
    ctrl: GeoDocCtrl,
    id: number,
    startPoint: RenderVertex | number[],
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    radius: number | undefined = undefined
) {
    return createSectorElement(ctrl, id, startPoint, centerPoint, endPoint, RenderSector, radius);
}

/**
 * Create a preview RenderAngle from two lines or three points, synchronized with other preview functions.
 * @param ctrl
 * @param id
 * @param root - Root point (vertex) of the angle (flatten Point, RenderVertex, or [x, y])
 * @param startV - Starting vector of the angle (vector, RenderLine, or two points)
 * @param endV - Ending vector of the angle (vector, RenderLine, or two points)
 * @param startVDir - Direction for start vector (1 or -1), default 1
 * @param endVDir - Direction for end vector (1 or -1), default 1
 * @returns RenderAngle
 */
export function pAngle(
    ctrl: GeoDocCtrl,
    id: number,
    root: Point | RenderVertex | number[],
    startV: Vector | RenderLine | number[] | [RenderVertex, RenderVertex] | [[number, number], [number, number]],
    endV: Vector | RenderLine | number[] | [RenderVertex, RenderVertex] | [[number, number], [number, number]],
    startVDir: 1 | -1 = 1,
    endVDir: 1 | -1 = 1
): RenderAngle {
    validatePreviewId(id);

    // Extract root coordinates
    let rootX: number, rootY: number;
    if (root instanceof RenderVertex) {
        [rootX, rootY] = root.coords;
    } else if (Array.isArray(root)) {
        [rootX, rootY] = root;
    } else {
        [rootX, rootY] = [root.x, root.y];
    }

    const refPEl: GeoRenderElement[] = [];

    // Always create a preview vertex for the root if not already a RenderVertex
    const intersectionVertex = root instanceof RenderVertex ? root : pVertex(id - 1, [rootX, rootY]);
    addRefPEl(intersectionVertex, refPEl);

    // Extract and orient start vector
    let _startV = extractVector(startV);
    _startV = [_startV[0] * startVDir, _startV[1] * startVDir, _startV[2] !== undefined ? _startV[2] : 0];
    if (startV instanceof RenderLine) {
        addRefPEl(startV, refPEl);
    } else if (Array.isArray(startV)) {
        addRefPEl(pVertex(id - 2, _startV), refPEl);
    }

    // Extract and orient end vector
    let _endV = extractVector(endV);
    _endV = [_endV[0] * endVDir, _endV[1] * endVDir, _endV[2] !== undefined ? _endV[2] : 0];
    if (endV instanceof RenderLine) {
        addRefPEl(endV, refPEl);
    } else if (Array.isArray(endV)) {
        addRefPEl(pVertex(id - 3, _endV), refPEl);
    }

    // Build the RenderAngle preview object
    const anglePartial: Partial<RenderAngle> = {
        relIndex: id,
        name: '',
        anglePointIdx: intersectionVertex.relIndex >= 0 ? intersectionVertex.relIndex : NOT_SET_VALUE,
        usable: true,
        valid: true,
        pInfo: {
            refPEl,
            cCoords: [rootX, rootY],
        },
    };

    const el = Object.assign(new RenderAngle(), anglePartial);
    el.vectorStart = _startV;
    el.vectorEnd = _endV;

    return el;
}

/**
 * Get reference element from index. This will first check the the renderer
 * if the element is there, then use it, if not it check the reference elements
 * included as part of the preview.
 * @param idx
 * @param host
 * @param renderer
 */
export function refElFromIdx(idx: number, host: GeoRenderElement, renderer: GeoRenderer): GeoRenderElement | undefined {
    const el = renderer.elementAt(idx);

    if (el) return el;

    const pel = renderer.previewElAt(idx);
    if (pel) return pel;

    const refEl = host.pInfo?.refPEl.find(re => re.relIndex == idx);
    if (refEl) return refEl;

    return undefined;
}

/**
 * Utility class to ensure only necessary preview is synchronized
 */
export class PreviewQueue {
    private queue: GeoRenderElement[] = [];

    add(...elements: GeoRenderElement[]): void {
        elements.forEach(element => {
            if (element) {
                // Ensure no duplicates by relIndex
                if (!this.queue.find(el => el.relIndex === element.relIndex)) {
                    this.queue.push(element);
                }
            }
        });
    }

    async flush(docCtrl: GeoDocCtrl): Promise<void> {
        if (!docCtrl || this.queue.length === 0) {
            this.queue = []; // Clear queue if no docCtrl or empty
            return;
        }

        const elementsToSync = new Map<number, GeoRenderElement>();
        this.queue.forEach(el => elementsToSync.set(el.relIndex, el));

        this.queue.forEach(el => {
            if (el.pInfo && el.pInfo.refPEl) {
                el.pInfo.refPEl.forEach(refEl => {
                    elementsToSync.delete(refEl.relIndex);
                });
            }
        });

        for (const el of Array.from(elementsToSync.values())) {
            await syncPreviewCommands(el, docCtrl);
        }
        this.queue = [];
    }
}

/**
 * Extract vector from various input types and normalize to [x, y, z] format
 * @param v - Input vector in various formats
 * @returns Normalized vector as [number, number, number]
 */
export function extractVector(
    v: Vector | RenderLine | number[] | [RenderVertex, RenderVertex] | [[number, number], [number, number]]
): [number, number, number] {
    if (v instanceof RenderLine) {
        const mustReverse =
            (v.startPointIdx >= 0 && v.endPointIdx >= 0 && v.startPointIdx > v.endPointIdx) ||
            (v.startPointIdx < 0 && v.endPointIdx >= 0);
        if (mustReverse) {
            return [v.vector[0] * -1, v.vector[1] * -1, v.vector[2] || 0];
        } else {
            return [v.vector[0], v.vector[1], v.vector[2] || 0];
        }
    } else if (Array.isArray(v)) {
        // Check if it's a simple number array [x, y] or [x, y, z]
        if (typeof v[0] === 'number') {
            const numArray = v as number[];
            return [numArray[0], numArray[1], numArray[2] || 0];
        }
        // Check if it's [RenderVertex, RenderVertex]
        else if (v.length === 2 && v[0] instanceof RenderVertex && v[1] instanceof RenderVertex) {
            const mustReverse =
                (v[0].relIndex >= 0 && v[1].relIndex >= 0 && v[0].relIndex > v[1].relIndex) ||
                (v[0].relIndex < 0 && v[1].relIndex >= 0);
            if (mustReverse) {
                return [v[1].coords[0] - v[0].coords[0], v[1].coords[1] - v[0].coords[1], 0];
            } else {
                return [v[0].coords[0] - v[1].coords[0], v[0].coords[1] - v[1].coords[1], 0];
            }
        }
        // Check if it's [[number, number], [number, number]]
        else if (v.length === 2 && Array.isArray(v[0]) && Array.isArray(v[1])) {
            const [[x1, y1], [x2, y2]] = v as [[number, number], [number, number]];
            const dx = x2 - x1;
            const dy = y2 - y1;
            return [dx, dy, 0];
        }
        // Fallback for unexpected array format
        else throw new GeoErr('Unexpected array format in extractVector');
    } else {
        // v is Vector from @flatten-js/core
        const vector = v as Vector;
        return [vector.x, vector.y, 0];
    }
}
