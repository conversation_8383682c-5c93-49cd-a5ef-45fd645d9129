import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderVector, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pPolygon, PreviewQueue, pVertex } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { nPoints, RepeatSelector, SelectedVertex, vert } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    assignNames,
    buildPreviewVertexRenderProp,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    remoteConstruct,
} from './tool.utils';

export class CreatePolygonTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePolygonTool';

    declare selLogic: RepeatSelector<SelectedVertex>;
    pQ = new PreviewQueue();
    firstPoint?: RenderVertex;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    createSelLogic() {
        this.selLogic = nPoints(this.pQ, this.pointerHandler.cursor, {
            onPartialSelection: this.newPointSelected.bind(this),
        });
        this.selLogic.setOption('onComplete', this.performConstruction.bind(this));
    }

    newPointSelected(newSel: SelectedVertex, curSel: SelectedVertex[], selector: any, doc: GeoDocCtrl): boolean {
        if (curSel.length >= 3) this.selLogic.get('vertex').setOption('preview', true); // enable selection of preview so the first point is created

        let complete = false;
        if (curSel.length > 2 && vert(newSel).relIndex == this.firstPoint?.relIndex) {
            // if new sel is the same as the first sel, then the polygon is completed
            complete = true;
        }
        return !complete;
    }

    override resetState() {
        this.selLogic.reset();
        this.selLogic.get('vertex').setOption('preview', false);
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (selected && selected.length > 1) {
            // add a preview point on top of the first selected point so that we can change its color for highlighting
            // and also allow its selection to complete the polygon
            const v = vert(selected[0]);
            this.firstPoint = pVertex(-21, v.coords);
            this.firstPoint.renderProp = buildPreviewVertexRenderProp();
            this.firstPoint.renderProp.pointColor = '#ff0000';
            this.pQ.add(this.firstPoint);

            const p = pPolygon(
                ctrl,
                -20,
                selected.map(s => vert(s).coords),
                true,
                RenderVector
            ); // we just draw the polygon without its vertices element, The individual element is synced by the vertex selector
            this.pQ.add(p);
        }

        this.pQ.flush(ctrl);
    }

    protected async performConstruction(selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
        selector.selected.splice(selector.selected.length - 1, 1); // remove the last point, because it is the same as the first point
        const { pcs, points, strokes } = await assignNames(
            ctrl,
            selector.selected,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Đa giác'
        );
        if (!pcs) {
            this.resetState();
            return;
        }
        const polygonName = points.map(p => p.name).join('');
        const constructionPolygon = this.buildPolygonConstruction(polygonName);

        try {
            await remoteConstruct(ctrl, constructionPolygon, pcs, this.editor.geoGateway, 'Đa giác');
        } finally {
            this.resetState();
        }
    }

    private buildPolygonConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Polygon/PolygonEC', 'Polygon', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }
}
