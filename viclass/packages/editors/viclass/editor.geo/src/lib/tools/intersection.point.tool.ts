import { arc, circle, line, point, vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator, UIPointerEventData } from '@viclass/editor.core';
import { AxiosError } from 'axios';
import { syncPreviewCommands, syncRenderCommands } from '../cmd';
import {
    intersectionCircleEllipse,
    intersectionEllipses,
    intersectionLineEllipse,
} from '../element.intersection/intersections';
import { geoDefaultHandlerFn, GeoErr, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    ParamSpecs,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/preview.util';
import { nthDirectionOnLine, nthDirectionRotation, sortByRotationV2 } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { repeat, RepeatSelector, stroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewVertexRenderProp,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    pickPointName,
    requestElementNames,
} from './tool.utils';

type IntersectionElementDetails = {
    name: string; // Name of the geometric element (e.g., "l1", "c1")
    elType?: string; // Specific type like "LineSegment" used in params if exists
    labelType: string; // Generic type for tpl: "Line", "Circle", "Ellipse", "Sector"
    defId: string; // ParamDefId for this element type: "aLine", "aCircle", etc.
};

type IntersectionRequestArgs = {
    cgName: string; // From Kotlin CGS enum, e.g., "LineLine"
    outputName: string;
    paramA: IntersectionElementDetails;
    paramB: IntersectionElementDetails;
    nth?: number; // 1-based index if present
};

function getElementDetails(el: RenderLine | RenderCircle | RenderEllipse | RenderSector): IntersectionElementDetails {
    if (isElementLine(el)) {
        const defId = (() => {
            switch (el.elType) {
                case 'Ray':
                    return 'aRay';
                case 'LineSegment':
                    return 'aLineSegment';
                case 'VectorVi':
                    return 'aVector';
                default:
                    return 'aLine';
            }
        })();
        return { name: el.name, elType: el.elType, labelType: 'Line', defId };
    } else if (el.type === 'RenderCircle') {
        return { name: el.name, elType: el.elType, labelType: 'Circle', defId: 'aCircle' };
    } else if (el.type === 'RenderEllipse') {
        return { name: el.name, elType: el.elType, labelType: 'Ellipse', defId: 'anEllipse' };
    } else if (el.type === 'RenderSector') {
        return { name: el.name, elType: el.elType, labelType: 'Sector', defId: 'aCircularSector' };
    }
    // Should not happen due to filtering
    throw new Error(`Unknown element type for intersection: ${el.type}`);
}

function buildIntersectionRequest(args: IntersectionRequestArgs): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'Point/IntersectionPointEC', // Matches Kotlin class name
        'Point', // Output element type
        args.cgName,
        args.outputName
    );

    const paramSpecs: ParamSpecs[] = [
        {
            paramDefId: args.paramA.defId,
            indexInCG: 0,
            optional: false,
            tplStrLangId: `tpl-IntersectionOf${args.paramA.labelType}`,
            params: {
                name: { type: 'singleValue', value: args.paramA.name },
            },
        },
        {
            paramDefId: args.paramB.defId,
            indexInCG: 1,
            optional: false,
            tplStrLangId: `tpl-IntersectionWith${args.paramB.labelType}`,
            params: {
                name: { type: 'singleValue', value: args.paramB.name },
            },
        },
    ];

    if (args.nth !== undefined) {
        paramSpecs.push({
            paramDefId: 'aValue', // nth parameter is always 'aValue'
            indexInCG: 2,
            optional: true, // nth is conceptually optional
            tplStrLangId: 'tpl-thIntersection',
            params: { value: { type: 'singleValue', value: args.nth.toString() } },
        });
    }
    construction.paramSpecs = paramSpecs;
    return construction;
}

export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    override readonly toolType: GeometryToolType = 'IntersectionPointTool';

    // Selector for choosing 2 stroke elements for intersection
    declare selLogic?: RepeatSelector<StrokeType>;

    private pQ = new PreviewQueue();
    private intersectionPreview: RenderVertex[] = [];
    private intersectionConstructed: RenderVertex[] = [];
    private selectedElements: StrokeType[] = [];
    private partialSelectedElements: StrokeType[] = [];

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    createSelLogic() {
        // Start with selecting 2 stroke elements for intersection
        this.selLogic = repeat<StrokeType>(
            stroke({
                selectableStrokeTypes: [
                    'RenderLine',
                    'RenderLineSegment',
                    'RenderVector',
                    'RenderRay',
                    'RenderCircle',
                    'RenderEllipse',
                    'RenderSector',
                ],
                previewQueue: this.pQ,
                cursor: this.pointerHandler.cursor,
                refinedFilter: this.excludeAlreadySelected.bind(this),
                highlightOnMatch: true,
            }),
            {
                count: 2,
                onComplete: this.onElementsSelected.bind(this),
                onPartialSelection: this.onPartialElementSelected.bind(this),
            }
        );

        console.log('IntersectionPointTool selector logic created successfully');
    }

    override resetState() {
        this.selLogic?.reset();
        this.selectedElements = [];
        this.partialSelectedElements = [];
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        super.resetState();
    }

    excludeAlreadySelected(el: StrokeType): boolean {
        return !this.partialSelectedElements.includes(el);
    }

    onPartialElementSelected(newSel: StrokeType, curSel: StrokeType[], _selector: any, _doc: GeoDocCtrl): boolean {
        // Update our tracking array with currently selected elements
        this.partialSelectedElements = [...curSel, newSel];
        console.log('Partial selection updated:', this.partialSelectedElements.length, 'elements');
        return true; // Continue selection
    }

    async onElementsSelected(selector: RepeatSelector<StrokeType>, doc: GeoDocCtrl) {
        // This will be called when 2 elements are selected
        this.selectedElements = selector.selected || [];
        console.log('onElementsSelected called with:', this.selectedElements.length, 'elements');
        console.log(
            'Element types:',
            this.selectedElements.map(el => el.type)
        );

        if (this.selectedElements.length === 2) {
            console.log('Starting intersection preview process...');
            await this.processPreviewIntersectionPoint(doc);
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        // If we have intersection previews, check if user clicked on one of them
        if (this.intersectionPreview.length > 0) {
            const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event, false, true);
            const hitEl = hitCtx?.hitDetails?.el;

            if (hitEl && hitEl.type === 'RenderVertex') {
                const previewPoint = this.intersectionPreview.find(p => p.relIndex === hitEl.relIndex);
                if (previewPoint) {
                    // User clicked on an intersection preview point
                    if (event.eventType === 'pointerup') {
                        this.processBuildConstruction(ctrl, previewPoint);
                    }
                    this.pQ.flush(ctrl);
                    return;
                }
            }
        }

        // If no intersection previews or no preview point clicked, use the selector logic
        const selected = this.selLogic?.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    async processIntersectionPreview(docCtrl: GeoDocCtrl) {
        if (this.selectedElements.length !== 2) return;

        // Clear previous intersection previews
        this.intersectionPreview = [];

        // Call the appropriate preview method based on element types
        await this.processPreviewIntersectionPoint(docCtrl);
    }

    updateSelectorForIntersectionPoints() {
        // Intersection preview points are now handled via direct click detection in doTrySelection
        console.log('Intersection preview points ready for selection');
    }

    // Process intersection preview using the selected elements
    private async processPreviewIntersectionPoint(docCtrl: GeoDocCtrl) {
        console.log('processPreviewIntersectionPoint called');
        if (this.selectedElements.length !== 2) {
            console.log('Not enough elements selected:', this.selectedElements.length);
            return;
        }
        if (this.intersectionPreview.length > 0) {
            console.log('Preview already exists');
            return;
        }

        const [element1, element2] = this.selectedElements;
        console.log('Processing intersection for:', element1.type, 'and', element2.type);

        // Determine intersection type and call appropriate preview method
        if (isElementLine(element1) && isElementLine(element2)) {
            await this.previewLineLineIntersection(docCtrl);
        } else if (
            (isElementLine(element1) && element2.type === 'RenderCircle') ||
            (element1.type === 'RenderCircle' && isElementLine(element2))
        ) {
            await this.previewLineCircleIntersection(docCtrl);
        } else if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') {
            await this.previewCircleCircleIntersection(docCtrl);
        } else if (
            (isElementLine(element1) && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && isElementLine(element2))
        ) {
            await this.previewLineEllipseIntersection(docCtrl);
        } else if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
        ) {
            await this.previewCircleEllipseIntersection(docCtrl);
        } else if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') {
            await this.previewEllipseEllipseIntersection(docCtrl);
        } else if (
            (isElementLine(element1) && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && isElementLine(element2))
        ) {
            await this.previewLineSectorIntersection(docCtrl);
        } else if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
        ) {
            await this.previewCircleSectorIntersection(docCtrl);
        } else if (
            (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
        ) {
            await this.previewEllipseSectorIntersection(docCtrl);
        } else if (element1.type === 'RenderSector' && element2.type === 'RenderSector') {
            await this.previewSectorSectorIntersection(docCtrl);
        }

        // After previewing intersections, update selector to allow selection of intersection points
        if (this.intersectionPreview.length > 0) {
            this.updateSelectorForIntersectionPoints();
        }
    }

    // --- Helper methods for preview intersection points ---

    private createPreviewVertex(coords: [number, number], relIndex: number): RenderVertex {
        return {
            relIndex,
            type: 'RenderVertex',
            elType: 'Point',
            coords,
            renderProp: buildPreviewVertexRenderProp(),
            usable: true,
            valid: true,
            name: undefined,
            unselectable: false,
        };
    }

    private async previewLineLineIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedLine1 = element1 as RenderLine;
        const selectedLine2 = element2 as RenderLine;

        const startPointLine1 = docCtrl.rendererCtrl.elementAt(selectedLine1.startPointIdx) as RenderVertex;
        const startPointLine2 = docCtrl.rendererCtrl.elementAt(selectedLine2.startPointIdx) as RenderVertex;

        const l1 = line(
            point(startPointLine1.coords[0], startPointLine1.coords[1]),
            vector(-selectedLine1.vector[1], selectedLine1.vector[0])
        );
        const l2 = line(
            point(startPointLine2.coords[0], startPointLine2.coords[1]),
            vector(-selectedLine2.vector[1], selectedLine2.vector[0])
        );

        const intersections = l1.intersect(l2);

        if (!intersections?.length) {
            console.log('No line-line intersections found');
            return;
        }

        // Create and display preview intersection point
        const pvP = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
        this.intersectionPreview.push(pvP);
        syncPreviewCommands(pvP, docCtrl);

        console.log('Line-line intersection preview created:', pvP.coords);
    }

    private async previewLineCircleIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedLine = (isElementLine(element1) ? element1 : element2) as RenderLine;
        const selectedCircle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;

        const layerRenderer = docCtrl.rendererCtrl;

        const centerPoint = layerRenderer.elementAt(selectedCircle.centerPointIdx) as RenderVertex;
        const startPoint = layerRenderer.elementAt(selectedLine.startPointIdx) as RenderVertex;

        const c = circle(point(centerPoint.coords[0], centerPoint.coords[1]), selectedCircle.radius);
        const l = line(
            point(startPoint.coords[0], startPoint.coords[1]),
            vector(-selectedLine.vector[1], selectedLine.vector[0])
        );

        const intersections = l.intersect(c);

        if (!intersections?.length) {
            console.log('No line-circle intersections found');
            return;
        }

        // All intersections returned by intersect are valid; no filtering with isIntersectionPointOnLine
        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single line-circle intersection preview created');
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (nthDirectionOnLine(selectedLine.vector, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    private async previewLineEllipseIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedLine = (isElementLine(element1) ? element1 : element2) as RenderLine;
        const selectedEllipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;

        const layerRenderer = docCtrl.rendererCtrl;

        const f1El = layerRenderer.elementAt(selectedEllipse.f1Idx) as RenderVertex;
        const f2El = layerRenderer.elementAt(selectedEllipse.f2Idx) as RenderVertex;
        const startPoint = layerRenderer.elementAt(selectedLine.startPointIdx) as RenderVertex;

        const l = line(
            point(startPoint.coords[0], startPoint.coords[1]),
            vector(-selectedLine.vector[1], selectedLine.vector[0])
        );

        const pF1 = point(f1El.coords[0], f1El.coords[1]);
        const pF2 = point(f2El.coords[0], f2El.coords[1]);
        const pC = point((pF1.x + pF2.x) / 2, (pF1.y + pF2.y) / 2);

        const intersections = intersectionLineEllipse(
            l,
            pC,
            selectedEllipse.a,
            selectedEllipse.b,
            selectedEllipse.rotate
        );

        if (!intersections?.length) {
            console.log('No line-ellipse intersections found');
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single line-ellipse intersection preview created');
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (nthDirectionOnLine(selectedLine.vector, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);
            console.log('Multiple line-ellipse intersection previews created');
        }
    }

    private async previewLineSectorIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedLine = (isElementLine(element1) ? element1 : element2) as RenderLine;
        const selectedSector = (element1.type === 'RenderSector' ? element1 : element2) as RenderSector;

        const layerRenderer = docCtrl.rendererCtrl;

        const center1 = docCtrl.rendererCtrl.elementAt(selectedSector.centerPointIdx) as RenderVertex;
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.endPointIdx) as RenderVertex;
        const pC1 = point(center1.coords[0], center1.coords[1]);
        const pS1 = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1 = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1O = vector(pC1, point(pC1.x + 10, pC1.y));
        const angleStart1 = vecC1O.angleTo(vector(pC1, pS1));
        const angleEnd1 = vecC1O.angleTo(vector(pC1, pE1));

        const lineStartPoint = layerRenderer.elementAt(selectedLine.startPointIdx) as RenderVertex;

        const curve = arc(pC1, selectedSector.radius, angleStart1, angleEnd1);
        const l = line(
            point(lineStartPoint.coords[0], lineStartPoint.coords[1]),
            vector(-selectedLine.vector[1], selectedLine.vector[0])
        );

        const intersections = l.intersect(curve);

        if (!intersections?.length) {
            console.log('No line-sector intersections found');
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single line-sector intersection preview created');
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (nthDirectionOnLine(selectedLine.vector, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);
            console.log('Multiple line-sector intersection previews created');
        }
    }

    private async previewCircleCircleIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedCircle1 = element1 as RenderCircle;
        const selectedCircle2 = element2 as RenderCircle;

        const p1 = docCtrl.rendererCtrl.elementAt(selectedCircle1.centerPointIdx) as RenderVertex;
        const p2 = docCtrl.rendererCtrl.elementAt(selectedCircle2.centerPointIdx) as RenderVertex;
        const c1 = circle(point(p1.coords[0], p1.coords[1]), selectedCircle1.radius);
        const c2 = circle(point(p2.coords[0], p2.coords[1]), selectedCircle2.radius);

        const intersections = c1.intersect(c2);

        if (!intersections?.length) {
            console.log('No circle-circle intersections found');
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single circle-circle intersection preview created');
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (
                nthDirectionRotation(
                    [p2.coords[0] - p1.coords[0], p2.coords[1] - p1.coords[1]],
                    p1.coords,
                    pvP1.coords,
                    [pvP1.coords, pvP2.coords]
                ) === 1
            ) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);
            console.log('Multiple circle-circle intersection previews created');
        }
    }

    private async previewCircleEllipseIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedCircle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
        const selectedEllipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;

        const layerRenderer = docCtrl.rendererCtrl;

        const centerCircle = layerRenderer.elementAt(selectedCircle.centerPointIdx) as RenderVertex;
        const pCCircle = point(centerCircle.coords[0], centerCircle.coords[1]);

        const f1El = layerRenderer.elementAt(selectedEllipse.f1Idx) as RenderVertex;
        const f2El = layerRenderer.elementAt(selectedEllipse.f2Idx) as RenderVertex;
        const pF1 = point(f1El.coords[0], f1El.coords[1]);
        const pF2 = point(f2El.coords[0], f2El.coords[1]);
        const pCEll = point((pF1.x + pF2.x) / 2, (pF1.y + pF2.y) / 2);

        const intersections = intersectionCircleEllipse(
            pCCircle,
            selectedCircle.radius,
            pCEll,
            selectedEllipse.a,
            selectedEllipse.b,
            selectedEllipse.rotate
        );

        if (!intersections?.length) {
            console.log('No circle-ellipse intersections found');
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single circle-ellipse intersection preview created');
            return;
        }

        const sortedIntersections = sortByRotationV2(vector(pCEll, pCCircle), pCEll, intersections);

        if (sortedIntersections.length > 1) {
            let i = 0;
            const pvPs = sortedIntersections.map(ints => this.createPreviewVertex([ints.x, ints.y], -9999 - i++));

            this.intersectionPreview.push(...pvPs);
            pvPs.forEach(p => syncPreviewCommands(p, docCtrl));
            console.log('Multiple circle-ellipse intersection previews created');
        }
    }

    private async previewCircleSectorIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedCircle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
        const selectedSector = (element1.type === 'RenderSector' ? element1 : element2) as RenderSector;
        const layerRenderer = docCtrl.rendererCtrl;

        const centerSector = docCtrl.rendererCtrl.elementAt(selectedSector.centerPointIdx) as RenderVertex;
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.endPointIdx) as RenderVertex;
        const pC1Sector = point(centerSector.coords[0], centerSector.coords[1]);
        const pS1Sector = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1Sector = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1OSector = vector(pC1Sector, point(pC1Sector.x + 10, pC1Sector.y));
        const angleStart1 = vecC1OSector.angleTo(vector(pC1Sector, pS1Sector));
        const angleEnd1 = vecC1OSector.angleTo(vector(pC1Sector, pE1Sector));

        const centerCircle = layerRenderer.elementAt(selectedCircle.centerPointIdx) as RenderVertex;
        const pCCircle = point(centerCircle.coords[0], centerCircle.coords[1]);

        const curveSector = arc(pC1Sector, selectedSector.radius, angleStart1, angleEnd1);
        const circle1 = circle(pCCircle, selectedCircle.radius);

        const intersections = circle1.intersect(curveSector);

        if (!intersections?.length) {
            console.log('No circle-sector intersections found');
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single circle-sector intersection preview created');
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            const vec = vector(pCCircle, pC1Sector);

            if (
                nthDirectionRotation([vec.x, vec.y], centerCircle.coords, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1
            ) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    private async previewEllipseEllipseIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedEllipse1 = element1 as RenderEllipse;
        const selectedEllipse2 = element2 as RenderEllipse;
        const f1Ell1 = docCtrl.rendererCtrl.elementAt(selectedEllipse1.f1Idx) as RenderVertex;
        const f2Ell1 = docCtrl.rendererCtrl.elementAt(selectedEllipse1.f2Idx) as RenderVertex;
        const pcEll1 = point((f1Ell1.coords[0] + f2Ell1.coords[0]) / 2, (f1Ell1.coords[1] + f2Ell1.coords[1]) / 2);
        const aEll1 = selectedEllipse1.a;
        const bEll1 = selectedEllipse1.b;
        const rotateEll1 = selectedEllipse1.rotate;

        const f1Ell2 = docCtrl.rendererCtrl.elementAt(selectedEllipse2.f1Idx) as RenderVertex;
        const f2Ell2 = docCtrl.rendererCtrl.elementAt(selectedEllipse2.f2Idx) as RenderVertex;
        const pcEll2 = point((f1Ell2.coords[0] + f2Ell2.coords[0]) / 2, (f1Ell2.coords[1] + f2Ell2.coords[1]) / 2);
        const aEll2 = selectedEllipse2.a;
        const bEll2 = selectedEllipse2.b;
        const rotateEll2 = selectedEllipse2.rotate;

        const intersections = intersectionEllipses(pcEll1, aEll1, bEll1, rotateEll1, pcEll2, aEll2, bEll2, rotateEll2);

        if (!intersections?.length) {
            console.log('No ellipse-ellipse intersections found');
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single ellipse-ellipse intersection preview created');
            return;
        }

        const sortedIntersections = sortByRotationV2(vector(pcEll1, pcEll2), pcEll1, intersections);

        if (sortedIntersections.length > 1) {
            let i = 0;
            const pvPs = sortedIntersections.map(ints => this.createPreviewVertex([ints.x, ints.y], -9999 - i++));

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                pvPs.find(p => p.relIndex === el.relIndex) != null;

            pvPs.forEach(p => syncPreviewCommands(p, docCtrl));
            this.intersectionPreview.push(...pvPs);
            console.log('Multiple ellipse-ellipse intersection previews created');
        }
    }

    private async previewEllipseSectorIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedEllipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
        const selectedSector = (element1.type === 'RenderSector' ? element1 : element2) as RenderSector;
        const layerRenderer = docCtrl.rendererCtrl;

        const centerSector = layerRenderer.elementAt(selectedSector.centerPointIdx) as RenderVertex;
        const pCSector = point(centerSector.coords[0], centerSector.coords[1]);

        const f1El = layerRenderer.elementAt(selectedEllipse.f1Idx) as RenderVertex;
        const f2El = layerRenderer.elementAt(selectedEllipse.f2Idx) as RenderVertex;
        const pF1 = point(f1El.coords[0], f1El.coords[1]);
        const pF2 = point(f2El.coords[0], f2El.coords[1]);
        const pCEll = point((pF1.x + pF2.x) / 2, (pF1.y + pF2.y) / 2);

        // Intersection with the full ellipse
        const intersectionsStep1 = intersectionCircleEllipse(
            pCSector,
            selectedSector.radius,
            pCEll,
            selectedEllipse.a,
            selectedEllipse.b,
            selectedEllipse.rotate
        );

        if (!intersectionsStep1?.length) {
            console.log('No ellipse-sector intersections found');
            return;
        }

        // Now filter these points to be within the sector's arc
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.endPointIdx) as RenderVertex;
        const pC1 = point(centerSector.coords[0], centerSector.coords[1]);
        const pS1 = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1 = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1O = vector(pC1, point(pC1.x + 10, pC1.y));
        const angleStart1 = vecC1O.angleTo(vector(pC1, pS1));
        const angleEnd1 = vecC1O.angleTo(vector(pC1, pE1));
        const curveSector = arc(pCSector, selectedSector.radius, angleStart1, angleEnd1);

        const validIntersections = intersectionsStep1.filter(i => curveSector.contains(i));

        if (!validIntersections.length) {
            console.log('No valid intersections found');
            return;
        }

        if (validIntersections.length === 1) {
            const pvP1 = this.createPreviewVertex([validIntersections[0].x, validIntersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single ellipse-sector intersection preview created');
            return;
        }

        const sortedValidIntersections = sortByRotationV2(vector(pCEll, pCSector), pCEll, validIntersections);

        if (sortedValidIntersections.length > 1) {
            let i = 0;
            const pvPs = sortedValidIntersections.map(ints => this.createPreviewVertex([ints.x, ints.y], -9999 - i++));

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                pvPs.find(p => p.relIndex === el.relIndex) != null;

            pvPs.forEach(p => syncPreviewCommands(p, docCtrl));
            this.intersectionPreview.push(...pvPs);
            console.log('Multiple ellipse-sector intersection previews created');
        }
    }

    private async previewSectorSectorIntersection(docCtrl: GeoDocCtrl) {
        const [element1, element2] = this.selectedElements;
        const selectedSector1 = element1 as RenderSector;
        const selectedSector2 = element2 as RenderSector;
        const center1 = docCtrl.rendererCtrl.elementAt(selectedSector1.centerPointIdx) as RenderVertex;
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector1.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector1.endPointIdx) as RenderVertex;
        const pC1 = point(center1.coords[0], center1.coords[1]);
        const pS1 = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1 = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1O = vector(pC1, point(pC1.x + 10, pC1.y));
        const angleStart1 = vecC1O.angleTo(vector(pC1, pS1));
        const angleEnd1 = vecC1O.angleTo(vector(pC1, pE1));

        const center2 = docCtrl.rendererCtrl.elementAt(selectedSector2.centerPointIdx) as RenderVertex;
        const startPoint2 = docCtrl.rendererCtrl.elementAt(selectedSector2.startPointIdx) as RenderVertex;
        const endPoint2 = docCtrl.rendererCtrl.elementAt(selectedSector2.endPointIdx) as RenderVertex;
        const pC2 = point(center2.coords[0], center2.coords[1]);
        const pS2 = point(startPoint2.coords[0], startPoint2.coords[1]);
        const pE2 = point(endPoint2.coords[0], endPoint2.coords[1]);
        const vecC2O = vector(pC2, point(pC2.x + 10, pC2.y));
        const angleStart2 = vecC2O.angleTo(vector(pC2, pS2));
        const angleEnd2 = vecC2O.angleTo(vector(pC2, pE2));

        const arc1 = arc(pC1, selectedSector1.radius, angleStart1, angleEnd1);
        const arc2 = arc(pC2, selectedSector2.radius, angleStart2, angleEnd2);

        const intersections = arc1.intersect(arc2);

        if (!intersections?.length) {
            console.log('No sector-sector intersections found');
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            syncPreviewCommands(pvP1, docCtrl);
            console.log('Single sector-sector intersection preview created');
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (
                nthDirectionRotation(
                    [center2.coords[0] - center1.coords[0], center2.coords[1] - center1.coords[1]],
                    center1.coords,
                    pvP1.coords,
                    [pvP2.coords]
                ) === 1
            ) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async processBuildConstruction(docCtrl: GeoDocCtrl, intersectionSelected: RenderVertex) {
        const intersectionName = (
            await requestElementNames(docCtrl, this.toolbar.getTool('NamingElementTool') as NamingElementTool, [
                {
                    objName: 'Giao Điểm',
                    originElement: [intersectionSelected],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0]?.[0];
        if (!intersectionName) return;

        let construction: GeoElConstructionRequest | undefined;

        // 16 trường hợp ghép cặp giữa line, circle, ellipse, sector
        const nthFromPreview = (relIdx: number) =>
            this.intersectionPreview.length === 1
                ? undefined // For single intersection, nth is not needed or can be considered 1 by default backend logic
                : this.intersectionPreview.map(i => i.relIndex).indexOf(relIdx) + 1;

        const currentNth = nthFromPreview(intersectionSelected.relIndex);

        if (this.selectedElements.length === 2) {
            const [element1, element2] = this.selectedElements;
            const elA = getElementDetails(element1 as RenderLine | RenderCircle | RenderEllipse | RenderSector);
            const elB = getElementDetails(element2 as RenderLine | RenderCircle | RenderEllipse | RenderSector);

            // Determine intersection type based on element types
            let cgName = '';
            if (isElementLine(element1) && isElementLine(element2)) {
                cgName = 'LineLine';
            } else if (
                (isElementLine(element1) && element2.type === 'RenderCircle') ||
                (element1.type === 'RenderCircle' && isElementLine(element2))
            ) {
                cgName = 'LineCircle';
            } else if (
                (isElementLine(element1) && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && isElementLine(element2))
            ) {
                cgName = 'LineEllipse';
            } else if (
                (isElementLine(element1) && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && isElementLine(element2))
            ) {
                cgName = 'LineSector';
            } else if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') {
                cgName = 'CircleCircle';
            } else if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
            ) {
                cgName = 'CircleEllipse';
            } else if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
            ) {
                cgName = 'CircleSector';
            } else if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') {
                cgName = 'EllipseEllipse';
            } else if (
                (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
            ) {
                cgName = 'EllipseSector';
            } else if (element1.type === 'RenderSector' && element2.type === 'RenderSector') {
                cgName = 'SectorSector';
            }

            if (cgName) {
                construction = buildIntersectionRequest({
                    cgName,
                    outputName: intersectionName,
                    paramA: elA,
                    paramB: elB,
                    nth: cgName === 'LineLine' ? undefined : currentNth,
                });
            }
        }

        if (!construction) return;

        try {
            this.intersectionConstructed.push(intersectionSelected);

            await docCtrl.editor.awarenessFeature.useAwareness(
                docCtrl.viewport.id,
                'Đang tạo giao điểm',
                buildDocumentAwarenessCmdOption(docCtrl.editor.awarenessConstructId, docCtrl),
                async () => {
                    // This callback runs with awareness shown.
                    // Errors within this async function will be caught by the outer try/catch.
                    const constructResponse = await this.editor.geoGateway.construct(docCtrl.state.globalId, [
                        {
                            construction: construction!,
                        },
                    ]);

                    await syncRenderCommands(constructResponse.render, docCtrl);
                    await addHistoryItemFromConstructionResponse(docCtrl, constructResponse);
                }
            );

            // This part runs after the awareness block completes without throwing.
            // It handles syncing previews for any *remaining* unconstructed intersection points.
            if (this.intersectionPreview.length > this.intersectionConstructed.length) {
                this.intersectionPreview
                    .filter(p => !this.intersectionConstructed.find(i => i.relIndex === p.relIndex))
                    .forEach(pvP => {
                        syncPreviewCommands(pvP, docCtrl);
                    });
            }
        } catch (e) {
            console.log(e, e instanceof AxiosError);
            if (e.response) {
                throw new GeoErr(e.response.data.toString(), e);
            } else throw new GeoErr('Có lỗi xảy ra trong quá trình tạo giao điểm', e);
        } finally {
            // Reset state regardless of success or failure
            this.resetState();
        }
    }
}
