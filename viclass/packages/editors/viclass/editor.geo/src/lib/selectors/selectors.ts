/**
 * Selectors are delegators that perform selection flows for the tools. This ensure consistent selection behavior
 * across different tools and allows for easy customization of selection behavior.
 *
 * Each selector is responsible for selecting a specific type of element. The tools initialize the selectors it needs
 * and pass the pointer events to them to check if any element is selected.
 *
 * The selector might use potential selection delegators to handle display bigger selection areas when in touch mode.
 *
 * Tools use selector to ensure consistent selection behavior across different tools. The flow of using selector is as follows:
 *
 * 1. <PERSON><PERSON> initializes the selectors it needs. For example,
 *   - Polygonal tools (rectangle, triangle, etc... ) initialize a single Vertex selector with repeat
 *   - Intersection, point-on-curve tools initializes a curve selector + a line selector
 *   - Perpendicular/parallel tools initializes a point selector & a line selector
 *   - Angle selectors initialize a point selector repeated 3 times or 2 line selectors
 * 2. <PERSON><PERSON> calls the trySelect method to see if any element is potentially selected.
 * 3. If a potential selection is found for the current pointer event, the tool will call the highlighter to highlight the potential element.
 * 4. If the tool want to select the element, it will call the selector accept method to accept the element.
 * 5. When accepted, the selector logic will call approriate onComplete callback to inform the tool that the selection is completed.
 */

import { Cursor, LocatableEvent, pointerCursor } from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { syncHighlightCmd, syncPreviewCommands, syncRemoveHighlightCmd } from '../cmd';
import { GeoRenderElement } from '../model';
import { PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';

export const PreviewIds = {
    stroke: -1000000,
    vertex: -10000000,
};

export type SelectableType = GeoRenderElement | GeoRenderElement[] | SelectableType[] | 'nothing';

export type SelectorOptions<T extends SelectableType> = {
    name?: string; // name of the selector

    // whether to automatically enlarge the selection area if hitting some elements
    // when this is true, calling try select and if there is a hit, the element
    // will be used for calling sticky automatically.
    // If the element is not hit when it is being set as sticky, it will be removed
    // from sticky state.
    stickyOnMatch?: boolean; // whether to set the element as sticky when it is matched
    autoAcceptOnPointerUp?: boolean;
    highlightOnMatch?: boolean;
    preview?: boolean; // whether to include preview elements in the selection
    renderEl?: boolean; // whether to select render elements

    // whether to generate preview elements when nothing matches the pointer event
    genPreview?: boolean;
    syncPreview?: boolean;
    previewQueue?: PreviewQueue;
    /**
     * Certain pointer types such as mouse don't really need to use stickiness.
     * Use this to specify the pointer type that should be sticky
     *
     * undefined means all
     */
    stickyPTypes?: string[] | 'all' | undefined;

    cursor?: BehaviorSubject<Cursor[] | undefined>;

    // refined filter function
    // this function is called in addition to the default filter based on element type, preview, and renderEl
    // it can be used to perform use case such as during the selection
    // we want to restrict the set of selectable object
    refinedFilter?: (el: GeoRenderElement) => boolean;

    onComplete?: (selector: ElementSelector<T>, doc: GeoDocCtrl) => void;

    onReset?: (selector: ElementSelector<T>) => void;
};

export abstract class ElementSelector<T extends SelectableType, OT extends SelectorOptions<T> = SelectorOptions<T>> {
    abstract multiple: boolean;
    // the selected element
    selected?: T;
    acceptMarked: boolean = false;
    markedSelect: T | undefined;

    protected curStickyEl?: T;

    /**
     * When an element selector is created to select a particular type of element
     * it needs an id to assign to the element if the element is to be created for preview
     * purpose. This is the id that will be assigned to the preview element. This id
     * remains the same for the duration of selecting the element.
     *
     * It is changed when the element selector is reset. This happens, FOR EXAMPLE,
     * when using repeat dsl to select vertices of a polygon. Everytime one vertex is selected
     * the repeat dsl will reset its internal selector to select a new vertex.
     *
     * When this id is zero, it means that the selector doesn't have have any id. This is typical for
     * DSL selectors such as repeat, and, or, either, then, etc...
     */
    public curGeneratedId: number = 0;

    constructor(protected options?: Partial<OT>) {
        this.selected = null;
    }

    abstract get isAccepted(): boolean;
    protected abstract set isAccepted(value: boolean);

    protected cachedCursor?: Cursor[] | undefined;
    protected hCursor: Cursor[] = pointerCursor;

    setOption<KeyType extends keyof OT>(key: KeyType, value: OT[KeyType]): typeof this {
        if (!this.options) {
            this.options = {};
        }

        this.options[key] = value;

        return this;
    }

    setOptions(options: Partial<OT>): typeof this {
        if (!this.options) {
            this.options = {};
        }

        Object.assign(this.options, options);

        return this;
    }

    getOption<KeyType extends keyof OT>(key: KeyType): OT[KeyType] | undefined {
        return this.options?.[key];
    }

    protected abstract resetId();

    /**
     * @param keepPreview whether or not still keep the preview element inside the doc
     * This is useful when a selector is used multiple time.
     */
    reset(keepPreview: boolean = false) {
        this.selected = undefined;
        this.curStickyEl = undefined;
        this.isAccepted = false;
        this.resetId();

        if (this.options?.onReset) this.options.onReset(this);
    }

    protected highlight(el: GeoRenderElement[], doc: GeoDocCtrl) {
        if (this.options?.cursor) {
            this.cachedCursor = this.options.cursor.value;
            this.options.cursor.next(this.hCursor);
        }
        syncHighlightCmd(el, doc);
    }

    protected removeHighlight(el: GeoRenderElement[], doc: GeoDocCtrl) {
        if (this.options?.cursor) {
            this.options.cursor.next(this.cachedCursor);
            this.cachedCursor = undefined;
        }

        syncRemoveHighlightCmd(el, doc);
    }

    protected syncPreviewCommands(el: GeoRenderElement, doc: GeoDocCtrl) {
        if (this.options?.previewQueue) this.options?.previewQueue.add(el);
        else syncPreviewCommands(el, doc);
    }

    finalizeAccept(doc: GeoDocCtrl) {
        if (this.acceptMarked) {
            this.selected = this.markedSelect;
            this.isAccepted = true;
            if (this.options?.onComplete) {
                this.options.onComplete(this, doc);
            }
            this.markedSelect = undefined;
            this.acceptMarked = false;
        }
    }

    protected accept(el: T, doc: GeoDocCtrl): void {
        this.markedSelect = el;
        this.acceptMarked = true; // must be before onComplete because onComplete might reset this selector
    }

    /**
     * Checks if the element is hit given the pointer event, if not, generate a preview depending
     * on the options
     * this is the wrap around of the tryHit and tryPreview.
     * @param event - The event to check.
     * @param doc
     * @returns True if the element is selected, false otherwise.
     */
    trySelect(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined {
        let hit = this.tryHit(event, doc);
        if (!hit) hit = this.tryPreview(event, doc);

        this.finalizeAccept(doc);

        if (this.isAccepted) hit = undefined;

        return hit;
    }

    /**
     * Check if any existing element match the event
     * @param event
     * @param doc
     */
    abstract tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;

    /**
     * Try to generate preview / remove preview with input from tryHit
     * @param event
     * @param doc
     */
    abstract tryPreview(event: LocatableEvent<any>, doc: GeoDocCtrl): T | undefined;

    abstract clearTrial(doc: GeoDocCtrl); // clear any preview created by this selector
}

type NothingSelectorOptions = Pick<SelectorOptions<'nothing'>, 'onComplete'>;

export function nothing(options?: NothingSelectorOptions): NothingSelector {
    return new NothingSelector();
}
/**
 * Nothing selector is a selector to catch the end of a selection logic
 * when nothing is selected and the user's click doesn't match any other selector.
 */
export class NothingSelector extends ElementSelector<'nothing'> {
    override multiple: boolean = false;
    private _isAccepted = false;

    constructor(protected override options?: NothingSelectorOptions) {
        super();
    }

    override get isAccepted(): boolean {
        return this._isAccepted;
    }

    protected override set isAccepted(value: boolean) {
        this._isAccepted = value;
    }

    protected override resetId() {}
    override tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): 'nothing' {
        return undefined; // always return undefined
    }
    override tryPreview(event: LocatableEvent<any>, doc: GeoDocCtrl): 'nothing' {
        // in try preview, we check for completion
        if (event.eventType == 'pointerup') this.accept('nothing', doc);
        return 'nothing';
    }

    override clearTrial(doc: GeoDocCtrl) {
        // do nothing, because this doesn't produce any preview
    }
}
