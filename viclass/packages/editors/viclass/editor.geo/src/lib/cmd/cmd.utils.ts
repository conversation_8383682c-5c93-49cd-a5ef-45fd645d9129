import { cmdMeta, CriticalErr, reliableCmdMeta, ViErr } from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.geo';
import {
    GeoRenderElement,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
} from '../model';
import { GeoDocCtrl } from '../objects';
import {
    EndPreviewCmd,
    HighlightElementsCmd,
    PotentialSelectionElementsCmd,
    PreviewAngleCmd,
    PreviewCircleCmd,
    PreviewCircleShapeCmd,
    PreviewLineCmd,
    PreviewPolygonCmd,
    PreviewSectorShapeCmd,
    RemoveHighlightElementsCmd,
    RemovePotentialSelectionElementsCmd,
    RemovePreviewElsCmd,
    RenderAngleCmd,
    RenderCircleCmd,
    RenderCircleShapeCmd,
    RenderEllipseCmd,
    RenderEllipseShapeCmd,
    RenderLineCmd,
    RenderPolygonCmd,
    RenderSectorCmd,
    RenderSectorShapeCmd,
    RenderVertexCmd,
    UpdateDocStateCmd,
} from './geo.cmd';
import { convertDocRenderPropToProto } from './proto.utils';

/**
 * Synchronizes rendering commands for a given set of GeoRenderElement objects.
 * This function processes each render element, creates the corresponding command, and sends it to the command channel.
 *
 * @param renders An array of GeoRenderElement objects to be processed.
 * @param docCtrl The document controller managing the rendering operations.
 * @throws {ViErr} If a known error occurs during synchronization.
 * @throws {CriticalErr} If an unexpected error occurs, prompting the user to reload the page.
 */
export async function syncRenderCommands(renders: GeoRenderElement[], docCtrl: GeoDocCtrl) {
    try {
        const layerCtrl = docCtrl.layers[0];

        for (const gre of renders) {
            let cmd = null;
            switch (gre.type) {
                case 'RenderVertex': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_VERTEX);
                    cmd = new RenderVertexCmd(meta);
                    cmd.setVertex(gre);
                    break;
                }
                case 'RenderLineSegment':
                case 'RenderVector':
                case 'RenderRay':
                case 'RenderLine': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_LINE);
                    cmd = new RenderLineCmd(meta);
                    cmd.setLine(gre);
                    break;
                }
                case 'RenderPolygon': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_POLYGON
                    );
                    cmd = new RenderPolygonCmd(meta);
                    cmd.setPolygon(gre);
                    break;
                }
                case 'RenderCircleShape': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_CIRCLE_SHAPE
                    );
                    cmd = new RenderCircleShapeCmd(meta);
                    cmd.setCircle(gre as RenderCircleShape);
                    break;
                }
                case 'RenderEllipseShape': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_ELLIPSE_SHAPE
                    );
                    cmd = new RenderEllipseShapeCmd(meta);
                    cmd.setEllipse(gre as RenderEllipseShape);
                    break;
                }
                case 'RenderSectorShape': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_CIRCULAR_SECTOR_SHAPE
                    );
                    cmd = new RenderSectorShapeCmd(meta);
                    cmd.setSector(gre as RenderSectorShape);
                    break;
                }
                case 'RenderAngle': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_ANGLE);
                    cmd = new RenderAngleCmd(meta);
                    cmd.setAngle(gre as RenderAngle);
                    break;
                }
                case 'RenderSector': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_SECTOR);
                    cmd = new RenderSectorCmd(meta);
                    cmd.setArc(gre as RenderSector);
                    break;
                }
                case 'RenderEllipse': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(
                        docCtrl.viewport,
                        docCtrl.state.id,
                        objId,
                        CmdTypeProto.RENDER_ELLIPSE
                    );
                    cmd = new RenderEllipseCmd(meta);
                    cmd.setArc(gre as RenderEllipse);
                    break;
                }
                case 'RenderCircle': {
                    const objId = gre.relIndex;
                    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, objId, CmdTypeProto.RENDER_CIRCLE);
                    cmd = new RenderCircleCmd(meta);
                    cmd.setArc(gre as RenderCircle);
                    break;
                }
                default:
                    break;
            }

            if (cmd) {
                cmd.setLayer(layerCtrl.state.id);
                await docCtrl.editor.cmdChannel.receive(cmd);
            }
        }
    } catch (e) {
        if (e instanceof ViErr) throw e;
        throw new CriticalErr('Có lỗi xảy ra khi đồng bộ hiển thị. Vui lòng tải lại trang', e);
    }
}

export async function syncUpdateDocStateCommand(docCtrl: GeoDocCtrl) {
    const doc = docCtrl.state;
    const meta = reliableCmdMeta(docCtrl.viewport, doc.id, -1, CmdTypeProto.UPDATE_DOC_STATE);
    const cmd = new UpdateDocStateCmd(meta);
    cmd.setState(doc.globalId, convertDocRenderPropToProto(doc.docRenderProp));
    await docCtrl.editor.cmdChannel.receive(cmd);
}

export async function syncEndPreviewModeCommand(docCtrl: GeoDocCtrl) {
    const layerCtrl = docCtrl.layers[0];
    const meta = reliableCmdMeta(docCtrl.viewport, docCtrl.state.id, -1, CmdTypeProto.END_PREVIEW);
    const cmd = new EndPreviewCmd(meta);
    cmd.setLayer(layerCtrl.state.id);
    await docCtrl.editor.cmdChannel.receive(cmd);
}

/**
 * Synchronizes preview rendering commands for a given GeoPreviewElement.
 * This function determines the appropriate command type based on the preview element and executes it through the command channel.
 *
 * @param el The GeoPreviewElement object to be processed.
 * @param docCtrl The document controller managing the rendering operations.
 * @param cmdType (Optional) The specific command type to be executed.
 * @throws {ViErr} If a known error occurs during synchronization.
 * @throws {CriticalErr} If an unexpected error occurs, prompting the user to reload the page.
 */
export async function syncPreviewCommands(el: GeoRenderElement, docCtrl: GeoDocCtrl, cmdType?: CmdTypeProto) {
    const layerCtrl = docCtrl.layers[0];
    let cmd = null;

    if (cmdType) {
        switch (cmdType) {
            default:
                break;
        }
    }

    if (!cmd) {
        switch (el.type) {
            case 'RenderVertex': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.RENDER_VERTEX);
                meta.versionable = docCtrl.state.id;
                cmd = new RenderVertexCmd(meta);

                (cmd as RenderVertexCmd).setVertex(el as RenderVertex);
                break;
            }
            case 'RenderLineSegment':
            case 'RenderVector':
            case 'RenderRay':
            case 'RenderLine': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_LINE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewLineCmd(meta);

                (cmd as PreviewLineCmd).setLine(el as RenderLine);
                break;
            }
            case 'RenderPolygon': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_POLYGON);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewPolygonCmd(meta);

                (cmd as PreviewPolygonCmd).setPolygon(el as RenderPolygon);
                break;
            }
            case 'RenderCircle': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_CIRCLE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewCircleCmd(meta);

                (cmd as PreviewCircleCmd).setArc(el as RenderCircle);

                break;
            }
            case 'RenderCircleShape': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_CIRCLE_SHAPE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewCircleShapeCmd(meta);

                (cmd as PreviewCircleShapeCmd).setCircle(el as RenderCircleShape);

                break;
            }
            case 'RenderEllipse':
            case 'RenderEllipseShape': {
                // const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_ELLIPSE_SHAPE);
                // meta.versionable = docCtrl.state.id;
                // cmd = new PreviewEllipseShapeCmd(meta);

                // const preview = setEllipsePInfo(el as RenderEllipse, docCtrl.rendererCtrl);

                // (cmd as PreviewEllipseShapeCmd).setEllipse(preview);
                break;
            }
            case 'RenderSector':
            case 'RenderSectorShape': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_CIRCULAR_SECTOR_SHAPE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewSectorShapeCmd(meta);
                (cmd as PreviewSectorShapeCmd).setSector(el as RenderSectorShape);
                break;
            }
            case 'RenderAngle': {
                const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.PREVIEW_ANGLE);
                meta.versionable = docCtrl.state.id;
                cmd = new PreviewAngleCmd(meta);
                cmd.setAngle(el as RenderAngle);
                break;
            }

            default:
                break;
        }
    }

    if (cmd) {
        cmd.setLayer(layerCtrl.state.id);
        await docCtrl.editor.cmdChannel.receive(cmd);
    }
}

/**
 * Remove preview from a doc controller.
 * @param preview the id of the preview render element or the preview element itself.
 * @param docCtrl the document controller containing the preview to be removed.
 */
export async function syncRemovePreviewCmd<T extends GeoRenderElement>(
    preview: T | number | T[] | number[],
    docCtrl: GeoDocCtrl
) {
    const meta = cmdMeta(docCtrl.viewport, docCtrl.state.id, CmdTypeProto.REMOVE_PREVIEW_BY_REL_IDS);
    meta.versionable = docCtrl.state.id;
    const cmd = new RemovePreviewElsCmd(meta);
    const list: (T | number)[] = !Array.isArray(preview) ? [preview] : preview;
    cmd.setState(list.map(l => (typeof l === 'number' ? l : l.relIndex)));

    await docCtrl.editor.cmdChannel.receive(cmd);
}

export async function syncHighlightCmd(els: GeoRenderElement[], doc: GeoDocCtrl) {
    const elIndexArr = els.map(el => el.relIndex);
    const curSelected = doc.selectedElements;

    if (curSelected && curSelected.length >= 0) {
        for (const el of curSelected) {
            if (elIndexArr.indexOf(el.relIndex) === -1) elIndexArr.push(el.relIndex);
        }
    }
    const layerCtrl = doc.layers[0];
    const meta = reliableCmdMeta(doc.viewport, doc.state.id, doc.state.id, CmdTypeProto.HIGHLIGHT_ELEMENTS);
    const cmd = new HighlightElementsCmd(meta);
    cmd.setState(layerCtrl.state.id, elIndexArr);
    await doc.editor.cmdChannel.receive(cmd);
}

export async function syncRemoveHighlightCmd(els: GeoRenderElement[], doc: GeoDocCtrl) {
    let elIndexArr = els.map(e => e.relIndex);
    const curSelected = doc.selectedElements?.map(e => e.relIndex);
    if (curSelected && curSelected.length >= 0) elIndexArr = elIndexArr.filter(idx => !curSelected.includes(idx));
    const layerCtrl = doc.layers[0];
    const meta = reliableCmdMeta(doc.viewport, doc.state.id, doc.state.id, CmdTypeProto.REMOVE_HIGHLIGHT_ELEMENTS);
    const cmd = new RemoveHighlightElementsCmd(meta);
    cmd.setState(layerCtrl.state.id, elIndexArr);
    await doc.editor.cmdChannel.receive(cmd);
}

export async function syncPotentialSelection(el: GeoRenderElement, doc: GeoDocCtrl) {
    const elIndexArr = [el.relIndex];
    const layerCtrl = doc.layers[0];
    const meta = reliableCmdMeta(doc.viewport, doc.state.id, doc.state.id, CmdTypeProto.POTENTIAL_SELECTION_ELEMENTS);
    const cmd = new PotentialSelectionElementsCmd(meta);
    cmd.setState(layerCtrl.state.id, elIndexArr);
    await doc.editor.cmdChannel.receive(cmd);
}

export async function syncRemovePotentialSelection(el: GeoRenderElement, doc: GeoDocCtrl) {
    const elIndexArr = [el.relIndex];
    const layerCtrl = doc.layers[0];
    const meta = reliableCmdMeta(
        doc.viewport,
        doc.state.id,
        doc.state.id,
        CmdTypeProto.REMOVE_POTENTIAL_SELECTION_ELEMENTS
    );
    const cmd = new RemovePotentialSelectionElementsCmd(meta);
    cmd.setState(layerCtrl.state.id, elIndexArr);
    await doc.editor.cmdChannel.receive(cmd);
}
