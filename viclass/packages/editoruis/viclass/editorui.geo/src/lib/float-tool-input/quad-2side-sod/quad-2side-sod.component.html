<div class="regpolygon-edge-input-overlay shadow-SH1 gap-1">
    <div class="flex justify-center items-center gap-[20px]">
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': (sod$ | async) == 0 }"
            (click)="setSOD(0)">
            Side
        </button>
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': (sod$ | async) == 1 }"
            (click)="setSOD(1)">
            Side 1
        </button>
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': (sod$ | async) == 2 }"
            (click)="setSOD(2)">
            Diagonal
        </button>
    </div>
</div>
